import pytest
import json
from unittest.mock import MagicMock, patch, ANY, call
import os
import logging
import time

os.environ["MODE"] = "worker"
os.environ["DOMAIN"] = "http://example.com"

from src.worker.messages_worker.messages_received_worker import (
    z_api_first_message,
    can_send_audio,
    check_strikes,
    handle_received_message,
    run as run_received,
)
from src.worker.messages_worker.messages_to_send_worker import (
    handle_send_message,
    run as run_send,
)
from src.worker.crm_worker.crm_worker import run as run_crm
from src.worker.bq_worker.updates_worker import run as run_updates
from src.worker.bq_worker.logs_worker import run as run_logs
import pytest
from unittest.mock import MagicMock, patch
from src.worker.workers import run_workers, WORKERS
from src.worker.health_check.start_health_check import start_health_check_server
from src.extras.util import monitor_health

@pytest.fixture
def bq_mock():
    with patch('src.worker.bq_worker.updates_worker.bq') as mock_bq:
        yield mock_bq

@pytest.fixture
def scraper_mock():
    with patch('src.worker.bq_worker.updates_worker.Scraper') as mock_scraper:
        yield mock_scraper

@pytest.fixture
def identify_voice_mock():
    with patch('src.worker.bq_worker.updates_worker.identify_voice') as mock_identify:
        yield mock_identify

@pytest.fixture
def openai_util_module_mock():
    with patch('src.worker.llm_modules.llm_utils.openai_util_module.OpenAIUtilModule') as mock_openai:
        yield mock_openai

@pytest.fixture
def redis_client():
    return MagicMock()

@pytest.fixture
def mock_set():
    with patch('src.worker.bq_worker.updates_worker.redis_client_set') as mock_set:
        yield mock_set

@pytest.fixture
def bq():
    return MagicMock()

class TestWorkers:
    @patch("src.integrations.pacto.tools.integration_tools.PactoIntegrationTools.get_url")
    @patch("src.integrations.pacto.tools.integration_tools.PactoIntegrationTools.search_by_phone")
    def test_z_api_first_message_user_not_found(self, mock_search_by_phone, mock_get_url, redis_client, bq):
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "admMsUrl": "http://fake-adm-ms"
        }
        mock_get_url.return_value = mock_response

        mock_search_by_phone.return_value = (False, {})

        telefone = "123456789"
        id_empresa = "teste-1"
        fase_crm = "LEAD"
        nome = "Test User"

        with patch('src.worker.messages_worker.messages_received_worker.bq', bq):
            with patch('src.worker.messages_worker.messages_received_worker.NotificationManager', bq):
                z_api_first_message(telefone, id_empresa, fase_crm, redis_client, nome)

        redis_client.lpush.assert_called_once()
        redis_client.set.assert_called()
        bq().save_message.assert_called_once_with("user", "Oi", telefone)

    @patch("src.integrations.pacto.tools.integration_tools.PactoIntegrationTools.get_url")
    @patch("src.integrations.pacto.tools.integration_tools.PactoIntegrationTools.search_by_phone")
    @patch("src.worker.messages_worker.messages_received_worker.register_indicator")
    def test_z_api_first_message_user_found(
        self, _mock_reigster_indicator, mock_search_by_phone,
        mock_get_url, redis_client, bq
    ):
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "admMsUrl": "http://fake-adm-ms"
        }
        mock_get_url.return_value = mock_response
        found_user = {
            "cliente": "123",
            "pessoa": {
                "nome": "Test User"
            },
            "fase_crm": "AT"
        }
        mock_search_by_phone.return_value = (True, found_user)

        telefone = "123456789"
        id_empresa = "teste-1"
        fase_crm = "LEAD"
        nome = "Test User"

        with patch('src.worker.messages_worker.messages_received_worker.bq', bq):
            z_api_first_message(telefone, id_empresa, fase_crm, redis_client, nome)

        redis_client.lpush.assert_called_once()
        redis_client.set.assert_called_once()
        bq().save_message.assert_called_once_with("user", "Oi", telefone)

    def test_can_send_audio(self):
        assert can_send_audio("Hello", "audio") == True
        assert can_send_audio("Hello http://example.com", "audio") == False
        assert can_send_audio("Hello", "text") == False
        assert can_send_audio("Hello", "audio", {"is_campanha": True}) == False
        assert can_send_audio("Hello", "audio", {"is_campanha": False}) == True

    def test_check_strikes(self, redis_client, bq):
        telefone = "123456789"
        id_empresa = "1"
        redis_client.get.return_value = json.dumps({"user_strikes": 3, "registered": False})

        assert check_strikes(telefone, id_empresa, redis_client, bq) == True
        redis_client.set.assert_called_once()

        redis_client.get.return_value = json.dumps({"user_strikes": 2, "registered": False})
        assert check_strikes(telefone, id_empresa, redis_client, bq) == False

    @patch("src.worker.messages_worker.messages_received_worker.get_from_instance")
    @patch("src.worker.messages_worker.messages_received_worker.unset_pendency_verification")
    @patch("src.worker.messages_worker.messages_received_worker.set_pendency_verification")
    @patch("src.worker.messages_worker.messages_received_worker.unset_retry_contact")
    @patch("src.worker.messages_worker.messages_received_worker.set_retry_contact")
    @patch("src.worker.messages_worker.messages_received_worker.connections")
    @patch("src.worker.messages_worker.messages_received_worker.register_indicator")
    def test_handle_received_message_no_strikes_recent_message(
        self,
        _mock_register_indicator,  # noop
        mock_connections,
        mock_set_retry_contact,
        mock_unset_retry_contact,
        mock_set_pendency_verification,
        mock_unset_pendency_verification,
        mock_get_from_instance,
        redis_client,
        bq
    ):
        task = {
            "id_empresa": None,
            "data": 
            {
                "connectedPhone": "123456789",
                "phone": "123456789",
                "text": {"message": "Hello"},
                "instance_id": "123456",
                "momment": time.time_ns() / 1_000_000,
                "departamento": None,
                "colaborador": None
            },
            "canAnswer": True,
            "origin": "z_api",
            "sessionId": None
        }
        print(time.time_ns())
        mock_get_from_instance.return_value = ("teste-1", "654321")
        # redis_client.get.return_value = json.dumps({"user_strikes": 0, "registered": False})
        redis_client.get.side_effect = [
            b'Hello',
            json.dumps({"user_strikes": 0, "registered": False})
            ]
        mock_connections.redis_client = redis_client
        with patch('src.worker.messages_worker.messages_received_worker.bq', bq):
            bq.return_value.get_user_context.return_value = (
                {
                    "aluno": {
                        "situacao": {
                            "codigo": "AT"
                        }
                    }
                },
                "AT"
            )
            with patch('src.worker.messages_worker.messages_received_worker.LLMSelector') as MockLLMSelector:
                MockLLMSelector().get_response.return_value = ("response", {"model": "openai", "prompt_tokens": 10, "completion_tokens": 10, "n_chars": 10})
                MockLLMSelector().get_info_pending_forwarding.return_value = None
                with patch('src.worker.messages_worker.messages_received_worker.WhatsappMessager') as MockWhatsappMessager:
                    handle_received_message(task)

        mock_connections.redis_client.lpush.assert_called_once()
        args, kwargs = mock_connections.redis_client.lpush.call_args
        assert args[0] == 'logs'
        log_data = json.loads(args[1])
        assert log_data["data"]["status"] == "success"
        assert log_data["data"]["phone"] == "+123456789"
        assert log_data["data"]["response"] == "response"
        assert log_data["data"]["id_empresa"] == "teste-1"
        assert "data_envio" in log_data["data"]
        assert log_data["table"] == "mensagens_enviadas"

    @patch("src.worker.messages_worker.messages_received_worker.get_from_instance")
    @patch("src.worker.messages_worker.messages_received_worker.unset_pendency_verification")
    @patch("src.worker.messages_worker.messages_received_worker.connections")
    def test_handle_received_message_no_strikes_old_message(self, mock_connections, mock_unset_pendency_verification, mock_get_from_instance, redis_client, bq):
        task = {                
            "id_empresa": None,
            "data": 
            {
                "connectedPhone": "123456789",
                "phone": "123456789",
                "text": {"message": "Hello"},
                "instance_id": "123456",
                "momment": 1726855579062,
                "departamento": None,
                "colaborador": None
            },
            "canAnswer": True,
            "origin": "z_api",
            "sessionId": None
        }
        mock_get_from_instance.return_value = ("teste-1", "654321")
        redis_client.get.side_effect = [
            b'Hello',
            json.dumps({"user_strikes": 0, "registered": False})
            ]
        mock_connections.redis_client = redis_client
        with patch('src.worker.messages_worker.messages_received_worker.bq', bq):
            bq.return_value.get_user_context.return_value = (
                {
                    "aluno": {
                        "situacao": {
                            "codigo": "AT"
                        }
                    }
                },
                "AT"
            )
            with patch('src.worker.messages_worker.messages_received_worker.LLMSelector') as MockLLMSelector:
                MockLLMSelector().get_response.return_value = ("response", {"prompt_tokens": 10, "completion_tokens": 10, "n_chars": 10})
                with patch('src.worker.messages_worker.messages_received_worker.WhatsappMessager') as MockWhatsappMessager:
                    handle_received_message(task)
        
        mock_connections.redis_client.lpush.assert_called_once()
        args, kwargs = mock_connections.redis_client.lpush.call_args
        assert args[0] == 'logs'
        log_data = json.loads(args[1])
        assert log_data["data"]["status"] == "error"
        assert log_data["data"]["phone"] == "+123456789"
        assert log_data["data"]["response"] == "Mensagem muito antiga."
        assert log_data["data"]["id_empresa"] == "teste-1"
        assert "data_envio" in log_data["data"]
        assert log_data["table"] == "mensagens_enviadas"


    @patch("src.worker.messages_worker.messages_received_worker.get_from_instance")
    @patch("src.worker.messages_worker.messages_received_worker.connections")
    @patch("src.worker.messages_worker.messages_received_worker.log_response")
    @patch("src.worker.messages_worker.messages_received_worker.datetime")
    def test_handle_received_message_with_strikes(
        self,
        mock_datetime,
        mock_log_response,
        mock_connections,
        mock_get_from_instance,
        redis_client,
        bq
    ):
        task = {
            "id_empresa": None,
            "data": {
                "connectedPhone": "123456789",
                "phone": "123456789",
                "text": {"message": "Hello"},
                "instance_id": "123456",
                "departamento": None,
                "colaborador": None
            },
            "canAnswer": True,
            "origin": "z_api",
            "sessionId": None
        }
        mock_get_from_instance.return_value = ("teste-1", "654321")
        redis_client.get.side_effect = [
            b'Hello',
            json.dumps({"user_strikes": 3, "registered": False})
            ]
        mock_connections.redis_client = redis_client
        mock_datetime.now.return_value.strftime.return_value = "2021-08-31T12:00:00"
        with patch('src.worker.messages_worker.messages_received_worker.bq', bq):
            bq.return_value.get_user_context.return_value = (
                {
                    "aluno": {
                        "situacao": {
                            "codigo": "AT"
                        }
                    }
                },
                "AT"
            )
            with patch('src.worker.messages_worker.messages_received_worker.LLMSelector') as MockLLMSelector:
                MockLLMSelector().get_response.return_value = ("response", {"prompt_tokens": 10, "completion_tokens": 10, "n_chars": 10})
                with patch('src.worker.messages_worker.messages_received_worker.WhatsappMessager') as MockWhatsappMessager:
                    handle_received_message(task)

        mock_log_response.assert_called_once_with(
            {
                "data": {
                    "status": "error",
                    "phone": "+123456789",
                    "response": "Usuário bloqueado por comportamento malicioso.",
                    "id_empresa": "teste-1",
                    "data_envio": "2021-08-31T12:00:00"
                },
                "table": "mensagens_enviadas"
            }
        )

    @patch("src.data.bigquery_data._postgres_handler.connections.postgres_connection")
    @patch("src.worker.messages_worker.messages_to_send_worker.register_indicator")
    def test_handle_send_message(
        self, _mock_register_indicator, bigquery_client_mock, redis_client
    ):
        task = {
            "id_empresa": "teste-1",
            "data": {
                "aluno": {
                    "pessoa": {
                        "nome": "Test User",
                        "telefonesconsulta": "123456789"
                    },
                    "fase_crm": "LEADS_HOJE",
                    "codigo": "123"
                }
            }
        }

        with patch('src.worker.messages_worker.messages_to_send_worker.WhatsappMessager'):
            with patch('src.worker.messages_worker.messages_to_send_worker.LLMSelector') as MockLLMSelector:
                MockLLMSelector().get_response.return_value = ("Resposta da IA", {"prompt_tokens": 10, "completion_tokens": 10, "n_chars": 10})
                response = handle_send_message(task, redis_client)

        # Verificar se a resposta está correta
        assert response["data"]["status"] == "success"
        assert response["data"]["phone"] == "123456789"
        assert response["data"]["response"] == "Resposta da IA"
        assert response["data"]["id_empresa"] == "teste-1"

        # Verificar se o Redis foi chamado corretamente
        redis_client.lpush.assert_any_call('task_queue_bd_contexts', ANY)
        assert redis_client.set.call_count == 3
        redis_client.set.assert_any_call('current_conversation:+123456789-teste-1', ANY)
        redis_client.set.assert_any_call('+123456789-teste-1', ANY, ex=ANY)
        redis_client.lpush.assert_any_call('task_queue_crm_msg_history', ANY)

    def test_run_messages_to_send_worker(self, redis_client):
        task = {
            "id_empresa": "1",
            "data": {
                "aluno": {
                    "pessoa": {
                        "nome": "Test User",
                        "telefonesconsulta": "123456789"
                    },
                    "fase_crm": "LEADS_HOJE",
                    "codigo": "123"
                }
            }
        }
        redis_client.brpop.return_value = ("messages_to_send".encode('utf-8'), json.dumps(task).encode('utf-8'))

        with patch('src.worker.messages_worker.messages_to_send_worker.handle_send_message') as mock_handle_send_message:
            mock_handle_send_message.return_value = {"data": {"status": "success"}}
            run_send(redis_client, max_iter=1)

        redis_client.brpop.assert_called()
        redis_client.lpush.assert_called_with('logs', ANY)
        
    def test_run_send_message_to_crm_worker(self, redis_client):
        # Dados de exemplo
        task_data = {
            "id_empresa": "123-456",
            "data": "2021-01-01T12:00:00",
            "message": "Test message",
            "cliente": "789",
            "tipoContato": "WA",
            "fase": "LEADS_HOJE"
        }
        task_json = json.dumps(task_data).encode('utf-8')

        # Mock do Redis blpop para retornar a tarefa e depois None
        redis_client.blpop.side_effect = [
            (None, task_json),
            None  # Simula que não há mais tarefas
        ]

        with patch('src.worker.crm_worker.crm_worker.PactoIntegrationTools') as MockPactoIntegrationTools:
            pacto_instance = MockPactoIntegrationTools.return_value
            pacto_instance.get_url.return_value = 'http://example.com'
            pacto_instance.get_token_auth.return_value = 'token123'


            # Mock do Redis usado em register_log
            with patch('src.extras.util.connections.redis_client', redis_client):
                with patch('src.worker.crm_worker.crm_worker.requests.post') as mock_post:
                    mock_response = MagicMock()
                    mock_response.status_code = 200
                    mock_post.return_value = mock_response

                    with patch('src.worker.crm_worker.crm_worker.register_log') as mock_register_log:
                        # Executa a função com max_iter=1 para evitar loop infinito
                        run_crm(redis_client, max_iter=1)

                        # Verificações
                        expected_url = 'http://example.com/v1/ia/conversa/historico-contato'
                        expected_headers = {
                            'Authorization': 'Bearer token123',
                            'empresaId': '456'
                        }
                        expected_body = {
                            'cliente': '789',
                            'message': 'Test message',
                            'tipoContato': 'WA',
                            'fase': 'LEADS_HOJE',
                            'data': '2021-01-01T12:00:00'
                        }
                        mock_post.assert_called_once_with(url=expected_url, json=expected_body, headers=expected_headers)
                        mock_register_log.assert_called_once_with(expected_url, expected_body, expected_headers, "POST", mock_response, "send_message_to_crm", '123-456')

    def test_run_gym_task(self, redis_client, bq_mock, scraper_mock):
        task = {
            'id_empresa': '1',
            'type': 'gym',
            'data': {
                'site': 'http://example.com',
                'sitescraping': True,
                'zapiToken': 'token',
                'zapiIdInstancia': 'instance',
                'usuarioPactoLogin': 'login',
                'usuarioPactoSenha': 'senha'
            }
        }
        redis_client.brpop.return_value = (None, json.dumps(task).encode('utf-8'))

        run_updates(redis_client, max_iter=1)

        bq_mock.return_value.save_instance_data.assert_called_with('instance', 'token')
        bq_mock.return_value.save_pacto_data.assert_called_with('login', 'senha')
        task['data']['site_texts'] = scraper_mock.return_value.text
        bq_mock.return_value.update_gym_context.assert_called_with(task['data'])
        scraper_mock.return_value.scrape.assert_called()

    def test_run_personality_task(
        self,
        mock_set,
        redis_client,
        bq_mock,
        identify_voice_mock,
        openai_util_module_mock
    ):
        identify_voice_mock.return_value = 'Voz masculina.'
        openai_util_module_mock.get_response.return_value.get.return_value = 'response'
        task = {
            'id_empresa': 'teste-2',
            'type': 'personality',
            'data': {
                'personalidade': 'Voz masculina.',
            }
        }
        redis_client.brpop.return_value = (None, json.dumps(task).encode('utf-8'))

        run_updates(redis_client, max_iter=1)

        bq_mock.return_value.update_personality_context.assert_called_with(task['data'])
        identify_voice_mock.assert_called_with('Voz masculina.', bq_mock.return_value)
        mock_set.assert_called_with(
            f"voice_schedule:{task['id_empresa']}",
            json.dumps(identify_voice_mock.return_value)
        )

    def test_run_plans_task(self, redis_client, bq_mock):
        task = {
            'id_empresa': '4',
            'type': 'plans',
            'data': {
                'plan_id': 'plan_123',
                'details': 'Plan details here'
            }
        }
        redis_client.brpop.return_value = (None, json.dumps(task).encode('utf-8'))
    
        run_updates(redis_client, max_iter=1)
    
        bq_mock.return_value.update_plans_context.assert_called_with(task['data'])
    
    def test_run_classes_task(self, redis_client, bq_mock):
        task = {
            'id_empresa': '5',
            'type': 'classes',
            'data': {
                'class_id': 'class_456',
                'details': 'Class details here'
            }
        }
        redis_client.brpop.return_value = (None, json.dumps(task).encode('utf-8'))
    
        run_updates(redis_client, max_iter=1)
    
        bq_mock.return_value.update_classes_context.assert_called_with(task['data'])
    
    def test_run_phases_task(self, redis_client, bq_mock):
        task = {
            'id_empresa': '6',
            'type': 'phases',
            'data': {
                'phase_id': 'phase_789',
                'details': 'Phase details here'
            }
        }
        redis_client.brpop.return_value = (None, json.dumps(task).encode('utf-8'))
    
        run_updates(redis_client, max_iter=1)
    
        bq_mock.return_value.update_phases_context.assert_called_with(task['data'])
    
    def test_run_products_task(self, redis_client, bq_mock):
        task = {
            'id_empresa': '7',
            'type': 'products',
            'data': {
                'product_id': 'prod_101',
                'details': 'Product details here'
            }
        }
        redis_client.brpop.return_value = (None, json.dumps(task).encode('utf-8'))
    
        run_updates(redis_client, max_iter=1)
    
        bq_mock.return_value.update_products_context.assert_called_with(task['data'])
    
    def test_run_user_task(self, redis_client, bq_mock):
        task = {
            'id_empresa': '8',
            'type': 'user',
            'data': {
                'telefone': '987654321',
                'fase': 'ACTIVE',
                'contexto': 'User context here',
                'origin_last_update': 'test_unit'
            }
        }
        redis_client.brpop.return_value = (None, json.dumps(task).encode('utf-8'))
    
        run_updates(redis_client, max_iter=1)
    
        bq_mock.return_value.save_user_context.assert_called_with(
            task['data']['contexto'],
            task['data']['telefone'],
            task['data']['fase'],
            origin=task['data']['origin_last_update']
        )

    def test_run_z_api_task(self, mock_set, redis_client, bq_mock):
        task = {
            'id_empresa': '9',
            'type': 'z_api',
            'data': {
                'instance_id': 'instance_202',
                'token': 'token_xyz'
            }
        }
        with patch('src.worker.bq_worker.updates_worker.get_device_from_z_api') as mock_get_device:
            mock_get_device.return_value = '+123456789'
            redis_client.brpop.return_value = (None, json.dumps(task).encode('utf-8'))
            run_updates(redis_client, max_iter=1)
            bq_mock.return_value.save_instance_data.assert_called_with(
                task['data']['instance_id'],
                task['data']['token']
            )

            # Verificações

            mock_set.assert_has_calls([
                call(f"instances:{task['id_empresa']}", json.dumps(task['data']), ex=28800),
                call(f"connected_phone:{task['id_empresa']}", "+123456789", ex=28800)
            ], any_order=False)

    @patch("src.worker.bq_worker.logs_worker.bq")
    def test_run_logs_task(self, mock_bigquery_data):
        # Configura tarefa de exemplo
        task = {
            "data": {
                "status": "error",
                "phone": "123456789",
                "response": "Teste unitário",
                "id_empresa": None,
                "data_envio": "2021-08-31T12:00:00"
            },
            "table": "mensagens_enviadas"
        }

        # Mock do Redis
        redis_client = MagicMock()
        redis_client.brpop.return_value = (None, json.dumps(task).encode("utf-8"))

        # Mock do BigQueryData e método register_log
        mock_bq_instance = mock_bigquery_data.return_value
        mock_bq_instance.register_log.return_value = None

        # Executa o worker
        run_logs(redis_client, max_iter=1)

        # Verifica que a tarefa foi consumida do Redis
        redis_client.brpop.assert_called_once_with("logs")

        # Atualiza o `id_empresa` no dado esperado
        expected_data = task["data"]
        
        # Verifica que register_log foi chamado com os argumentos corretos
        mock_bq_instance.register_log.assert_called_once_with(expected_data, task["table"])


@patch("src.worker.workers.Process")
@patch("src.worker.workers.monitor_health")
def test_run_workers(mock_monitor_health, mock_process, redis_client):
    mock_process_instance = MagicMock()
    mock_process.return_value = mock_process_instance

    run_workers(redis_client)

    # Verifica se o número de chamadas ao Process é igual ao número de WORKERS + 1 para o servidor de health check
    assert mock_process.call_count == len(WORKERS) + 1

    # Verifica se cada worker foi iniciado com a função correta
    for worker in WORKERS:
        mock_monitor_health.assert_any_call(worker.run)
        mock_process.assert_any_call(target=mock_monitor_health.return_value, args=(redis_client,))

    # Verifica se o servidor de health check foi iniciado
    mock_process.assert_any_call(target=start_health_check_server)

    # Verifica se todos os processos foram iniciados
    assert mock_process_instance.start.call_count == len(WORKERS) + 1

def test_health_check_process(redis_client):
    with patch("src.worker.workers.Process") as mock_process:
        mock_process_instance = MagicMock()
        mock_process.return_value = mock_process_instance

        run_workers(redis_client)

        # Verifica se o servidor de health check foi iniciado
        mock_process.assert_any_call(target=start_health_check_server)
        mock_process_instance.start.assert_called()


if __name__ == '__main__':
    pytest.main(args=["-vv"])
