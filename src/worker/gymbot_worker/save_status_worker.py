from datetime import datetime
import logging
import json
import uuid


import concurrent.futures
from concurrent.futures import TimeoutError

from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools
from src.extras.util import parse_phone, WorkersTracer, register_indicator
from src.data.bigquery_data import get_from_telefone, BigQueryData
from src.worker.entrypoint import connections 
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

SAVE_STATUS_MAX_WORKERS = int(os.getenv("SAVE_STATUS_MAX_WORKERS", "1"))

def save_status(task):
    id_empresa = task['id_empresa']

    logger.info(f" [SAVE_STATUS] ({id_empresa}) Chegou nova task: {json.dumps(task, indent=2, ensure_ascii=False)}")
    data = task['data']
    provedor = task.get('provedor', "z_api")

    bq_ = BigQueryData(id_empresa=id_empresa)

    match provedor:
        case "gymbot":
            gbit = GymbotIntegrationTools(id_empresa)
            logger.info(f" [SAVE_STATUS] ({id_empresa}) (gymbot) Carregou gbit para empresa {id_empresa}")
                    
            status = data.get("status_gymbot")
            session_id = data.get("sessionId")

            conversa = gbit.get_conversa(session_id, includeDetails=["ContactDetails"])
            logger.info(f"[SAVE_STATUS] ({id_empresa}) (gymbot) conversa: {conversa}")
            

            contato = conversa.get("contactDetails", None)
            telefone = contato["phonenumber"]

            status_data = {
                "id": data.get("id"),
                "status": status,
                "phone": telefone,
                "isGroup": data.get("isGroup", False),
                "provedor_mensagem": "gym_bot"
            }

            bq_.save_message_status(status_data)
            logger.info(f"[SAVE_STATUS] ({id_empresa}-{telefone}) (gymbot) iniciou processo de salvar no BQ")

        case 'z_api':
            bq_.save_message_status(data)
            logger.info(f"[SAVE_STATUS] ({id_empresa}) (z_api) iniciou processo de salvar no BQ")


@WorkersTracer(
    span_name_prefix=__name__,
    span_description="save status conversation worker",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter",
        "id_empresa": "id_empresa"
    }
)
def run(redis_client, max_iter=None):
    logger.info(" [SAVE_STATUS] Waiting for tasks")
    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        queue_size = max(redis_client.llen('save_status'), 1)
        n_tasks = min(SAVE_STATUS_MAX_WORKERS, queue_size)

        tasks = [redis_client.brpop('save_status') for _ in range(n_tasks)]
        if tasks:
            try:
                with concurrent.futures.ProcessPoolExecutor(max_workers=SAVE_STATUS_MAX_WORKERS) as executor:
                    for task in tasks:
                        task = json.loads(task[1].decode('utf-8'))
                        executor.submit(save_status, task)
                        id_empresa = task['id_empresa']
                        provedor = task.get('provedor', "z_api")
                        logger.info(f"[SAVE_STATUS] ({id_empresa}) ({provedor}) iniciou processo de salvar no BQ")
            except Exception as e:
                logger.error(f"[SAVE_STATUS] ({id_empresa}) Erro ao salvar status da conversa: {e}")
            logger.info(" [x] Done")

        iter_count += 1
