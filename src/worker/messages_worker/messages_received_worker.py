import json
import logging
import os
from datetime import datetime

from typing import Optional, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed

from requests.exceptions import ConnectTimeout, JSONDecodeError, ReadTimeout

from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools
from src.connections.delayed_queue import DelayedQueue
from src.extras.config import Config
from src.data.bigquery_data import (
    BigQueryData as bq,
    get_from_instance,
    is_rede as _is_rede
)
from src.extras.util import (
    parse_phone, retry, determine_voice,
    identify_pendency, log_func_name, add_custom_handler,
    register_indicator, run_in_thread
)
from src.data.data_processor import DataProcessor as dp
from src.worker.tts_modules.openai_tts.openai_tts_module import convert_to_audio
from src.connections.connections import Connections
from src.worker.messager_modules.whatsapp_messager.whatsapp_messager import WhatsappMessager
if os.getenv('MODE') == 'worker':
    from src.worker.llm_modules.llm_selector import LLMSelector
elif os.getenv('MODE') == 'router':
    from src.routerllm.routerllm_response_module import RouterLLMResponseModule as LLMSelector
else:
    raise ValueError(" [*] [messages_received_worker] MODE environment variable not set")
from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools
from src.worker.tracing import tracer, extract, inject
from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools as GBIT
from src.extras.util import WorkersTracer
from src.worker.llm_modules.llm_utils.helpers.notification_manager import (
    NotificationManager, NotificationRules
)
from src.extras.commands import Command, parse_command

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

connections = Connections.get_instance()
delayed_queue = DelayedQueue()

voices = { "feminino": "nova", "masculino": "onyx" }

SECONDS_TO_WAIT_TILL_RESPONSE = int(os.getenv("SECONDS_TO_WAIT_TILL_RESPONSE", '5'))
GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DATABASE = os.getenv("DATABASE", "postgres")

@log_func_name
def log_response(response: dict) -> None:
    logger.info(f" [*] [messages_received_worker] Response: {response}")
    if response is not None:
        connections.redis_client.lpush('logs', json.dumps(response))
    # Reseta o handler para retirar o número
    add_custom_handler(logger)


@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.validate_notification",
    span_description="Validando se a notificação é válida",
)
def validate_notification(
    data: dict,
) -> bool:
    logger.info("Validando se a mensagem pode ser enviada.")
    if not data.get("requiresValidation", False):
        return True
    rules = NotificationRules()
    return rules.validate(
        for_=data.get("validationType", None),
        data=data,
        value=data.get("validateValue", None),
    )


@run_in_thread
@log_func_name
def save_and_sync_messages(
    id_empresa: str,
    user_response: str,
    telefone: str,
    user_msg_type: str,
    user_info_msg: dict,
    message_id: str,
    messager_provider: str,
    id_contexto: str,
    situacao: str,
    departamento: str,
    colaborador: str,
    send_audio: bool,
    llm_infos: dict,
    old_llm_infos: dict,
    llm_response: str,
    old_llm_response: str,
    retry_audio: bool,
    sent_message_id: str,
    tts_seconds: float,
    user_context: dict,
    current_time_user: str,
    reset_conversation: bool = False,
) -> None:
    redis_client = connections.redis_client
    bq_ = bq(id_empresa=id_empresa)

    name = user_context.get('aluno', {}).get('pessoa', {}).get('nome')

    if user_msg_type == "audio":
        register_indicator(
            identificador="mensagem_audio",
            id_empresa=id_empresa,
            indicador="recebida",
            telefone=telefone,
            nome=name
        )

    bq_.save_message(
        "user",
        user_response,
        telefone,
        message_type=user_msg_type,
        n_chars=len(user_response),
        n_seconds=user_info_msg.get('n_seconds', 0),
        message_id=message_id,
        provider=messager_provider,
        id_contexto=id_contexto,
        situacao=situacao,
        departamento=departamento,
        colaborador=colaborador
    )

    if send_audio and llm_infos:
        if retry_audio and old_llm_infos is not None:
            bq_.save_message(
                "assistant",
                old_llm_response,
                telefone,
                message_type="text",
                prompt_tokens=old_llm_infos.get("prompt_tokens", 0),
                completion_tokens=old_llm_infos.get("completion_tokens", 0),
                n_chars=old_llm_infos.get("n_chars", 0),
                model=llm_infos.get("model", ""),
                provider=messager_provider,
                message_id=sent_message_id,
                id_contexto=id_contexto,
                situacao=situacao,
                departamento=departamento,
                colaborador=colaborador
            )
        bq_.save_message(
            "assistant",
            llm_response,
            telefone,
            message_type="audio",
            prompt_tokens=llm_infos.get("prompt_tokens", 0),
            completion_tokens=llm_infos.get("completion_tokens", 0),
            n_chars=llm_infos.get("n_chars", 0),
            n_seconds=tts_seconds,
            model=llm_infos.get("model", ""),
            provider=messager_provider,
            message_id=sent_message_id,
            id_contexto=id_contexto,
            situacao=situacao,
            departamento=departamento,
            colaborador=colaborador
        )
        register_indicator(
            identificador="mensagem_audio",
            id_empresa=id_empresa,
            indicador="enviada",
            telefone=telefone,
            nome=name
        )
    elif llm_infos:
        bq_.save_message(
            "assistant",
            llm_response,
            telefone,
            message_type="text",
            prompt_tokens=llm_infos["prompt_tokens"],
            completion_tokens=llm_infos["completion_tokens"],
            n_chars=llm_infos["n_chars"],
            model=llm_infos["model"],
            provider=messager_provider,
            message_id=sent_message_id,
            id_contexto=id_contexto,
            situacao=situacao,
            departamento=departamento,
            colaborador=colaborador
        )

    if user_context and user_context and 'aluno' in user_context and 'codigo' in user_context['aluno']:
        if user_context['aluno']['codigo']:
            user_task = {"id_empresa": id_empresa,
                         "message": f"USER: {user_response}",
                         "cliente": user_context['aluno']['codigo'],
                         "tipoContato": "WA",
                         "fase": id_contexto,
                         "data": current_time_user}
            redis_client.lpush('task_queue_crm_msg_history',
                               json.dumps(user_task))

            current_time = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')

            redis_client.lpush('task_queue_crm_msg_history',
                               json.dumps({"id_empresa": id_empresa,
                                           "message": f"IA: {llm_response}",
                                           "cliente": user_context['aluno']['codigo'],
                                           "tipoContato": "WA",
                                           "fase": id_contexto,
                                           "data": current_time}))
        else:
            logger.info(" [*] [messages_received_worker] No aluno code found")
    else:
        logger.info(" [*] [messages_received_worker] No aluno found")

    if reset_conversation:
        logger.info(f" [*] [messages_received_worker] Resetando conversa para {telefone} na empresa {id_empresa}")
        key = f"last_messages-{telefone}-{id_empresa}"
        logger.info(f" [*] [messages_received_worker] Deleting key: {key}")
        deleted_count = connections.redis_client.delete(
            key
        )
        logger.info(f" [*] [messages_received_worker] {deleted_count} keys deleted")


@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.verify_can_answer",
    span_description="Verificando se a conversa pode ser respondida"
)
def verify_can_answer(id_session, id_empresa):
    """
    Verifica se a conversa pode ser respondida.
    """
    gbit = GBIT(id_empresa)
    conversa = gbit.get_conversa(id_session)
    user_departamentId = conversa.get("departmentId", None)
    _, departaments = gbit.get_departamento()
    logger.info(f" [*] [verify_can_answer] conversa: {json.dumps(conversa, indent=4)}")
    ai_departamentId = [d for d in departaments if d["name"] == "ConversasAI"][0]["id"]
    logger.info(f" [*] [verify_can_answer] user_departamentId: {user_departamentId}, ai_departamentId: {ai_departamentId}")
    return user_departamentId == ai_departamentId

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.retry_send_message",
    span_description="Reenviando a mensagem por meio da fila `messages_received`"
)
def retry_send_message(original_task: dict):
    """
    Re-envia a mensagem por meio da fila `messages_received`.
    """
    if (retries := original_task.get('retries', 0) > 0):
        logger.info(" [*] [messages_received_worker] Retrying message again.")
        original_task['retries'] = retries + 1
    else:
        retries = 1
        original_task['retries'] = retries
    original_task['data']['text'] = {
        'message': "INFORMAÇÃO DO SISTEMA: ```SEM RESPOSTA DO USUÁRIO - Tente manter o engajamento.```"
    }

    context = original_task.get('context', {})
    with tracer.start_as_current_span(f"process_retry_{retries}", context=extract(context)) as span:
        span.set_attribute("id_empresa", original_task["id_empresa"])
        span.set_attribute("phone_number", original_task["data"]["phone"])
        span.set_attribute("model_source", "")
        span.set_attribute("origin", "")
        carrier = {}
        inject(carrier)
        original_task['context'] = carrier
        router_received_message(original_task)

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.set_retry_contact",
    span_description="Estabelecendo um novo contato com o usuário"
)
def set_retry_contact(telefone, id_empresa, original_task):
    """
    Estabelece um novo contato com o usuário X horas após o último contato.

    Args:
        telefone (str): Número de telefone do usuário.
        id_empresa (str): ID da empresa.
        original_task (dict): Tarefa original.
    """
    retry_contact_time = int(os.getenv("RETRY_CONTACT_TIME", '10'))
    logger.info(" [*] [messages_received_worker] Setting retry contact for %s", telefone)
    try:
        task_id = delayed_queue.delay_call(
            func=retry_send_message,
            args={"original_task": original_task},
            minutes=retry_contact_time
        )
        logger.info(" [*] [messages_received_worker] Task ID: %s", task_id)
        connections.redis_client.set(f"retry_contact:{telefone}-{id_empresa}", task_id, ex=2*60*60)
    except Exception as e:
        logger.error(" [*] [messages_received_worker] Error to set retry contact: %s", e)

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.unset_retry_contact",
    span_description="Removendo a tarefa de recontato agendada para o usuário"
)
def unset_retry_contact(telefone, id_empresa):
    """
    Remove a tarefa de recontato agendada para o usuário.

    Args:
        telefone (str): Número de telefone do usuário.
        id_empresa (str): ID da empresa.
    """
    logger.info(" [*] [messages_received_worker] Unsetting retry contact for %s", telefone)
    task_id = connections.redis_client.get(f"retry_contact:{telefone}-{id_empresa}")
    if task_id:
        task_id = task_id.decode('utf-8')
        delayed_queue.cancel_task(task_id)
        connections.redis_client.delete(f"retry_contact:{telefone}-{id_empresa}")

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.handle_retry_message",
    span_description="Gerenciando o reenvio de mensagens",
)
def handle_retry_message(
        task: dict,
        telefone_envio,
        id_empresa,
        bq_: bq,
        llm: LLMSelector,
        messager: WhatsappMessager
        ) -> Optional[Dict]:
    """
    Gerencia o reenvio de mensagens.

    Args:
        task (dict): Tarefa original.
        telefone_envio (str): Número de telefone do usuário.
        id_empresa (str): ID da empresa.
        llm (LLMSelector): Instância do seletor de modelos de linguagem.
    """
    print(json.dumps(task, indent=4))
    if 'retries' in task:
        retries = int(task['retries'])
        if retries == 1:
            logger.info(" [*] [messages_received_worker] Tentando fazer o reenvio da mensagem")
            unset_retry_contact(telefone_envio, id_empresa)
            set_retry_contact(telefone_envio, id_empresa, task)
        else:
            logger.info(" [*] [messages_received_worker] Finalizando conversa")
            llm.end_conversation()
            messager.end_conversation(
                phone=telefone_envio,
                id_empresa=id_empresa,
                sticker=bq_.get_sticker()
            )
            unset_retry_contact(telefone_envio, id_empresa)
            return {
                "data": {
                    "status": "success",
                    "phone": telefone_envio,
                    "response": "Conversa finalizada",
                    "id_empresa": id_empresa,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            }
    else:
        unset_retry_contact(telefone_envio, id_empresa)
        set_retry_contact(telefone_envio, id_empresa, task)

    return None

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.z_api_first_message",
    span_description="Enviando primeira mensagem para o usuário",
    span_attributes={
        "telefone": "telefone",
        "id_empresa": "id_empresa",
        "fase_crm": "fase_crm",
        "nome": "nome",
        "id_matriz": "id_matriz",
        "is_rede": "is_rede"
    }
)
def z_api_first_message(telefone: str, id_empresa: str, fase_crm: str, redis_client, nome="DESCONHECIDO", id_matriz: str = None, is_rede: bool = False):
    user_data = {
        "aluno": {
            "codigo": None,
            "pessoa": {
                "nome": f"{nome}",
                "telefonesconsulta": f"{telefone}",
            },
            "situacao": f"LEAD",
            "fase_crm": f"{fase_crm}",
        },
        "fase_atual": fase_crm
    }

    bq_ = bq(id_empresa=id_empresa)

    if is_rede:
        rede_json = bq(id_empresa=id_matriz).get_chain_context(extra_query=f"OR id_empresa = '{id_matriz}'")
        lista_empresas = [id_matriz] + [rede.get("id_empresa") for rede in rede_json] if rede_json else []
    else:
        logger.info(f" [*] [messages_received_worker] zapi first message: id_empresa: {id_empresa}")
        lista_empresas = [id_empresa]

    logger.info(f" [*] [messages_received_worker] Lista de empresas: {lista_empresas}")

    user_found, response = False, {}
    num_treads =  int(os.getenv('NUM_THREADS', '4'))
    num_workers = max(min(len(lista_empresas), num_treads), 1)
    pit_list = [PactoIntegrationTools(id_empresa_) for id_empresa_ in lista_empresas]
    logger.info("Iniciando processamento em paralelo para buscar pelo Telefone.")
    
    with ThreadPoolExecutor(max_workers = num_workers) as executor:
        futures = {
            executor.submit(pit.search_by_phone, telefone): pit
            for pit in pit_list
        }
        
        for future in as_completed(futures):
            pit = futures[future]
            id_empresa_ = pit.id_empresa
            
            try:
                user_found, response = future.result()
                logger.info(f" [*] [messages_received_worker] Resposta: {response} para a empresa {id_empresa_}")
            except (ConnectTimeout, JSONDecodeError, TimeoutError, ReadTimeout) as e:
                logger.error(f" [*] [messages_received_worker] Erro na requisição: {e} na função search_by_phone para a empresa {id_empresa_}.")
                continue
            
            if user_found:
                logger.info(" [*] [z_api_first] Usuário encontrado no Sistema Pacto pelo telefone")
                if isinstance(response, dict):
                    user_data = json.dumps(response)
                elif isinstance(response, str):
                    user_data["aluno"]["pessoa"]["nome"] = response
                    user_data = json.dumps(user_data)

                # Isso necessário só quando é uma rede, pois nesse caso o bq é instanciado id_empresa = None.
                # Se entrou aqui quer dizer que achei o usuário pelo telefone, então eu preciso instanciar o bq com o id_empresa correta.
                # Se não for uma rede, o codigo abaixo é redundante, mas não é um problema.
                id_empresa = id_empresa_
                bq_.set_id_empresa(id_empresa)

                register_indicator(
                    identificador="aluno_identificado",
                    id_empresa=id_empresa,
                    indicador="telefone",
                    telefone=telefone,
                    nome=response
                )
                break

    if not user_found:
        logger.info(
            " [*] [z_api_first] Usuário não encontrado no Sistema Pacto pelo telefone"
        )
        user_data = json.dumps(user_data)
        redis_client.set(f"{telefone}-waiting_cpf", "True", ex=86400)
        # Agendar régua de comunicação com LEAD
        logger.info(
            " [*] [z_api_first] Criando régua de contato com o LEAD"
        )
        NotificationManager(
            id_empresa=id_empresa,
            class_attributes={
                "telefone": telefone,
                "origin": "z_api",
            }
        ).schedule(
            for_="phase_message_send",
            kwargs={
                "phase": "LEADS_HOJE",
                "name": nome
            }
        )

    logger.info(
        " [*] [messages_received_worker] Usuário encontrado no Z-API: %s",
        json.dumps(user_data, indent=2, ensure_ascii=False)
    )
    bq_.save_message("user", "Oi", telefone)
    data = {
        "type": "user",
        "id_empresa": None if not user_found and is_rede else id_empresa,
        "id_matriz": id_matriz,
        "data":
            {
                "telefone": telefone,
                "contexto": user_data,
                "fase": fase_crm,
                "origin_last_update": "z_api_first_message"
            }
    }
    redis_client.lpush('task_queue_bd_contexts', json.dumps(data))
    redis_client.set(f"{telefone}-{id_empresa}", user_data, ex=8*60*60)
    if is_rede and user_found:
        redis_client.set(f"save_empresa:{id_matriz}-{telefone}", id_empresa)

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.can_send_audio",
    span_description="Verificando se a mensagem pode ser enviada como áudio",
    span_attributes={
        "text": "text",
        "last_message_type": "last_message_type",
        "info_llm": "info_llm"
    }
)
def can_send_audio(text, last_message_type, info_llm={"is_campanha": False}):
    """Verifica se a mensagem pode ser enviada como áudio"""
    patterns = ["http://", "https://", "www.", "@"]
    no_links = not any([pattern in str(text) for pattern in patterns]) # se pelo menos um dos padrões estiver presente, não envie áudio

    is_last_msg_audio = last_message_type == "audio" # olha se a última mensagem foi um áudio
    is_campanha = info_llm.get("is_campanha", False) # se for uma campanha, não envie áudio

    #logger.info(f"\nHá Links no texto? {no_links}\nÚltima mensagem foi Audio? {is_last_msg_audio}\nO texto é curto? {short_text}\nO tamanho original do texto é {len(text)}\nTamanho depois de limpar: {text_size}")
    return no_links and is_last_msg_audio and not is_campanha

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.check_strikes",
    span_description="Verificando se o usuário está bloqueado",
    span_attributes={
        "telefone": "telefone",
        "id_empresa": "id_empresa"
    }
)
def check_strikes(telefone, id_empresa, redis_client, bq_: bq) -> bool:
    strikes = redis_client.get(f"strikes:{telefone}-{id_empresa}")
    if strikes:
        user_strikes = int(json.loads(strikes).get('user_strikes', 0))
        registered = bool(json.loads(strikes).get('registered', False))
        logger.info(f"Strikes: {strikes}")
        if user_strikes >= 3:
            if not registered:
                QUERY = f"INSERT INTO {GCP_BIGQUERY_DATASET}.strikes_usuarios (telefone, id_empresa, data_registro) VALUES ('{telefone}', '{id_empresa}', '{datetime.now().strftime('%Y-%m-%dT%H:%M:%S')}')"
                bq_._save_dataframe_to_bq(QUERY)
                redis_client.set(f"strikes:{telefone}-{id_empresa}", json.dumps({"user_strikes": user_strikes, "registered": True}), ex=24*60*60)
            return True
    return False

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.check_pendency",
    span_description="Verificando se a pendência existe",
    span_attributes={
        "telefone": "telefone",
        "id_empresa": "id_empresa",
        "original_task": "original_task"
    }
)
def check_pendency(telefone, id_empresa, original_task):
    logger.info(f" [*] [messages_received_worker] Checking pendency for {telefone}")
    bq_ = bq(id_empresa=id_empresa)
    has_pendency, response = identify_pendency(id_empresa, telefone, bq_)
    if has_pendency:
        logger.info(f" [*] [messages_received_worker] Pendency found for {telefone}")
        message = f"""INFORMAÇÃO DO SISTEMA:
        ```
        Esta pendência foi encontrada: {response}
        Você pode ignorá-la utilizando a função dont_respond.
        ```
        """
        if not original_task.get('data', {}).get('text', None):
            original_task['data']['text'] = {}
        original_task['data']['text']['message'] = message
        original_task['data']['pendency_processed'] = True
        connections.redis_client.lpush('messages_received', json.dumps(original_task))
    
        context = original_task.get('context', {})
        with tracer.start_as_current_span("process_pendency", context=extract(context)) as span:
            span.set_attribute("id_empresa", id_empresa)
            span.set_attribute("phone_number", original_task["data"]["phone"])
            span.set_attribute("model_source", "routerllm")
            span.set_attribute("origin", "z_api")
            carrier = {}
            inject(carrier)
            original_task['context'] = carrier
            router_received_message(original_task)

@WorkersTracer(
    span_name_prefix=f"{__name__}.set_pendency_verification",
    span_description="Estabelecendo um novo contato com o usuário",
    span_attributes={
        "id_empresa": "id_empresa",
        "task_id": "task_id",
        "original_task": "original_task",
        "pendency_verification_time": "pendency_verification_time"
    }
)
def set_pendency_verification(telefone, id_empresa, original_task):
    pendency_verification_time = int(os.getenv("PENDENCY_VERIFICATION_TIME", 5))
    logger.info(f" [*] [messages_received_worker] Setting pendency verification for {telefone}")
    task_id = delayed_queue.delay_call(
        func=check_pendency,
        args={"telefone": telefone, "id_empresa": id_empresa, "original_task": original_task},
        minutes=pendency_verification_time
    )
    connections.redis_client.set(f"pendency_verification:{telefone}-{id_empresa}", task_id, ex=24*60*60)

@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.unset_pendency_verification",
    span_description="Removendo a tarefa de verificação de pendência agendada para o usuário",
    span_attributes={
        "id_empresa": "id_empresa",
        "telefone": "telefone",
        "task_id": "task_id",
        "original_task": "original_task"
    }
)
def unset_pendency_verification(telefone, id_empresa):
    logger.info(f" [*] [messages_received_worker] Unsetting pendency verification for {telefone}")
    task_id = connections.redis_client.get(f"pendency_verification:{telefone}-{id_empresa}")
    if task_id:
        task_id = task_id.decode('utf-8')
        delayed_queue.cancel_task(task_id)
        connections.redis_client.delete(f"pendency_verification:{telefone}-{id_empresa}")


@log_func_name
@WorkersTracer(
    span_name_prefix=f"{__name__}.ignore_message",
    span_description="Verificando se a mensagem deve ser ignorada",
    span_attributes={
        "momment": "momment"
    }
)
def ignore_message(mommment):
    """Verifica se a mensagem deve ser ignorada"""
    ignore = False
    # moment will be something like 1726855579062
    time_since = datetime.now() - datetime.fromtimestamp(mommment/1000)
    if time_since.total_seconds() > 60*60*5:
        ignore = True
    return ignore


@retry(retries=3, delay=2, backoff=2, status_codes=(500,))
@WorkersTracer(
    span_name_prefix=f"{__name__}.handle_received_message",
    span_description="Processando a mensagem recebida",
    span_attributes={
        "task": "task",
        "id_empresa": "id_empresa",
        "telefone": "telefone",
        "id_session": "id_session",
        "origin": "origin",
        "departamento": "departamento",
        "colaborador": "colaborador",
        "current_time_user": "current_time_user",
        "canAnswer": "canAnswer"
    }
)
def handle_received_message(task) -> None:
    if not task.get("tempo_processamento_meta", None):
        task['tempo_processamento_meta'] = {}
    # Obter a data e hora atual no formato ISO 8601
    before_setup = datetime.now()
    redis_client = connections.redis_client
    current_time_user = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')

    id_empresa = task['id_empresa']
    canAnswer = task['canAnswer']
    data = task['data']
    id_session= task.get('data', {}).get('sessionId', None)
    origin = task.get('origin', 'z_api')
    departamento = task['data']['departamento']
    colaborador = task['data']['colaborador']

    if origin == 'gym_bot' and canAnswer:
        # isso é necesssário devido as mensagens do retry_contact que não passam pelo task_adapter
        # Por isso, pode ser que quando retry entrou na fila o usuário estava no departamento da IA e agora já está em outro.
        canAnswer = verify_can_answer(id_session, id_empresa)

    data = json.loads(json.dumps(data))
    #logger.info(f'MENSAGEM RECEBIDA:\n{json.dumps(data, indent=4)}')
    logger.info(f'ID_EMPRESA: {id_empresa}')

    testing = False
    if data.get("sendToWebhook", None):
        testing = True

    if data.get('chave_empresa', None) is None:
        instance_id = data.get('instanceId')
        logger.info(f'INSTANCE_ID: {instance_id}')
        id_empresa, _ = get_from_instance(instance_id)

        if testing:
            id_empresa = data.get('testCompanyId', None)
            if not id_empresa:
                logger.error(" [*] [messages_received_worker] Test company ID not found")
                return
            origin = "auto_tests"

        if id_empresa is None:
            return log_response({
                "data":{
                    "status": "error",
                    "phone": data.get('phone', data.get('telefone', None)),
                    "response": "Empresa não encontrada",
                    "id_empresa": None,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            })
        is_group = bool(data.get('isGroup', False))
        user_info_msg, content = dp.process_message(data)
        if user_info_msg is None:
            return log_response({
                "data":{
                    "status": "error",
                    "phone": data.get('phone', data.get('telefone', None)),
                    "response": "Não há dados na mensagem",
                    "id_empresa": id_empresa,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            })
        user_msg_type = user_info_msg.get('type', None)
        message_id = data.get('messageId', None)
        id_session = data.get('sessionId', None)

        logger.info(f"[DEBUG] user_msg_type: {user_msg_type}")
        if user_msg_type == "audio":
            user_response = str(content)
        elif user_msg_type == "text":
            logger.info(f"[DEBUG] Text message received")
            phone = parse_phone(data.get('phone', None))
            user_response = redis_client.get(f"recent_messages:{id_empresa}:{phone}")
            if user_response:
                user_response = user_response.decode('utf-8')
            else:
                logger.warning(" [*] [messages_received_worker] No recent messages found for %s, empresa %s", phone, id_empresa)
                user_response = content.get("message")
            redis_client.delete(f"recent_messages:{id_empresa}:{phone}")
            redis_client.delete(f"messages_tasks:{id_empresa}:{phone}:task_id")
        elif user_msg_type == "notification":
            return log_response({"success": "success"})
        elif user_msg_type is None:
            user_response = "O usuário te mandou um tipo de mídia não suportado"
        else:
            user_response = f"O usuário te mandou uma mensagem do tipo {user_msg_type}, esse é o conteúdo: {json.dumps(content)}"

    elif data.get('chave_empresa', None) is not None:
        id_empresa = data.get('chave_empresa', None)
        telefone = data.get('telefone', None)
        user_response = data.get('mensagem', None)
        is_group = False
    
        user_msg_type = "text"
        message_id = None
        connected_phone = None
        id_session = None

    else:
        return log_response({
            "data":{
                "status": "error",
                "phone": data.get('phone', data.get('telefone', None)),
                "response": "Erro ao identificar a empresa",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })
    
    if is_group:
        telefone = parse_phone(data.get('participantPhone', None))
        telefone_envio = data.get('phone', None)

    elif not is_group:
        telefone = parse_phone(data.get('phone', data.get('telefone', None)))
        telefone_envio = telefone

    else:
        return log_response({
            "data": {
                "status": "error",
                "phone": telefone_envio,
                "response": "Erro ao identificar o telefone",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table": "mensagens_enviadas"
        })

    task["tempo_processamento_meta"]["tempo_validacao_dos_dados"] = (datetime.now() - before_setup).total_seconds()


    before_context = datetime.now()
    bq_ = bq(id_empresa=id_empresa)
    user_context, id_contexto = bq_.get_user_context(telefone=telefone)

    if not user_context:
        nome = data.get('senderName', 'DESCONHECIDO')
        z_api_first_message(telefone, id_empresa, "LEADS_HOJE", redis_client, nome)
        user_context, id_contexto = bq_.get_user_context(telefone=telefone)

    if not validate_notification(
        {**data, **user_context}
    ):
        logger.info(" [*] [messages_received_worker] Notificação inválida, ignorando")
        return log_response({
            "data": {
                "status": "error",
                "phone": telefone_envio,
                "response": "Usuário não está em uma situação que condiz com a notificação",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table": "mensagens_enviadas"
        })

    situacao = user_context.get('aluno', {}).get('situacao', None)
    if isinstance(situacao, dict):
        situacao = situacao.get('codigo', None)

    task["tempo_processamento_meta"]["tempo_busca_dados_usuario"] = (datetime.now() - before_context).total_seconds()

    before_validation = datetime.now()
    if not canAnswer:
        unset_pendency_verification(telefone_envio, id_empresa)
        unset_retry_contact(telefone_envio, id_empresa)
        bq_.save_message(
            "user",
            user_response,
            telefone,
            message_type=user_msg_type,
            n_chars=len(user_response),
            n_seconds=user_info_msg.get('n_seconds', 0),
            message_id=message_id,
            provider=origin,
            id_contexto=id_contexto,
            situacao=situacao,
            departamento=departamento,
            colaborador=colaborador
        )
        if user_context and user_context and 'aluno' in user_context and 'codigo' in user_context['aluno']:
            if user_context['aluno']['codigo']:
                user_task = {"id_empresa":id_empresa,
                            "message":f"USER: {user_response}",
                            "cliente": user_context['aluno']['codigo'],
                            "tipoContato": "WA",
                            "fase": id_contexto,
                            "data": current_time_user}
                redis_client.lpush('task_queue_crm_msg_history', json.dumps(user_task))

        return log_response({
            "data": {
                "status": "success",
                "phone": telefone_envio,
                "response": "IA não pode responder, pois o usuário está em outro departamento",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })

    logger.info(f"\n\nresposta usuario: {user_response}\n\n")

    if check_strikes(telefone, id_empresa, redis_client, bq_):
        return log_response({
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Usuário bloqueado por comportamento malicioso.",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })

    if ignore_message(data.get('momment')):
        return log_response({
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Mensagem muito antiga.",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })

    task["tempo_processamento_meta"]["tempo_validacao_usuario"] = (datetime.now() - before_validation).total_seconds()

    is_new_conversation = False
    try:
        new_conversation = task.get("new_conversation")
        logger.info("[IS_NEW_CONVERSATION] %s", new_conversation)
        task["new_conversation"] = False
        is_new_conversation = new_conversation or False
    except KeyError:
        pass

    before_context_update = datetime.now()
    if is_new_conversation:
        register_indicator(
            "conversa_iniciada",
            id_empresa,
            telefone=telefone,
            indicador="passivo",
            nome=user_context.get("aluno", {}).get("pessoa", {}).get("nome", "")
        )
        pacto_tools = PactoIntegrationTools(id_empresa)
        id_usuario = user_context.get("aluno", {}).get("codigo")
        if id_usuario:
            user_data = pacto_tools.get_user_by_id(id_usuario)
            if user_data:
                data = {
                    "type": "user",
                    "id_empresa": id_empresa,
                    "id_matriz": None,
                    "data":
                        {
                            "telefone": telefone,
                            "contexto": user_data,
                            "fase": id_contexto,
                            "origin_last_update": "new_conversation"
                        }
                }
                redis_client.lpush('task_queue_bd_contexts', json.dumps(data))
                redis_client.set(f"{telefone}-{id_empresa}", json.dumps(user_data))
                user_context = user_data

    task["tempo_processamento_meta"]["tempo_atualizacao_contexto_usuario"] = (datetime.now() - before_context_update).total_seconds()

    before_llm = datetime.now()

    model_source = bq_.get_model_source()

    test_args = {
        "webhook_url": data.get('webhookUrl', None),
        "test_name": data.get('testName', None)
    }

    llm = LLMSelector(
        user_context=user_context,
        id_empresa=id_empresa,
        phase=id_contexto,
        telefone=telefone,
        is_group=is_group,
        models_source=model_source,
        origin=origin,
        id_session=id_session,
        send_logs_to=test_args,
    )
    task["tempo_processamento_meta"]["tempo_construcao_prompt"] = (datetime.now() - before_llm).total_seconds()

    messager = WhatsappMessager(
        origin,
        id_empresa=id_empresa,
        test_args=test_args,
    )

    if (command := parse_command(user_response, telefone_envio, id_empresa)) != Command.NOOP:
        unset_retry_contact(telefone_envio, id_empresa)
        unset_pendency_verification(telefone, id_empresa)
        message = command.value
        return_value = {}
        match command:
            case Command.DONE:
                return_value = {
                    "data": {
                        "status": "success",
                        "phone": telefone_envio,
                        "response": "Operação realizada!",
                        "id_empresa": id_empresa,
                        "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                    },
                }
            case Command.FINISH:
                llm.end_conversation()
                return_value = {
                    "data": {
                        "status": "success",
                        "phone": telefone_envio,
                        "response": "Conversa finalizada",
                        "id_empresa": id_empresa,
                        "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                    },
                }

        messager.send_message(
            message_data=message,
            phone=telefone_envio,
            flow=Config.FLOW_NAME,
            id_empresa=id_empresa,
            message_type="text",
            message_id=message_id,
            is_group=is_group,
            id_session=id_session,
            connected_phone=parse_phone(data.get('connectedPhone'), origin=origin),
        )
        return log_response(return_value)

    is_retry = task.get('retries', 0) > 0
    if (retry_response := handle_retry_message(
            task,
            telefone_envio,
            id_empresa,
            bq_,
            llm,
            messager
            )) is not None:
        return log_response(retry_response)

    before_llm_message = datetime.now()

    llm_response, llm_infos = llm.get_response(user_response)

    task["tempo_processamento_meta"]["tempo_gerar_resposta_llm"] = (datetime.now() - before_llm_message).total_seconds()

    logger.info(f"Resposta LLM:\n{llm_response}" if llm_response else "LLM não pode responder!")
    if llm_response == "FINISH":
        messager.end_conversation(
            phone=telefone_envio,
            id_empresa=id_empresa,
            sticker=bq_.get_sticker()
            )
        unset_retry_contact(telefone_envio, id_empresa)
        unset_pendency_verification(telefone, id_empresa)
        return log_response({
            "data": {
                "status": "success",
                "phone": telefone_envio,
                "response": "Conversa finalizada",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
        })


    send_audio = can_send_audio(text=llm_response, last_message_type=user_msg_type, info_llm=llm_infos)
    message_type = "image" if llm_infos.get('is_campanha', False) else "text"
    message_data = llm_response

    if send_audio:
        retry_audio = False
        old_llm_response, old_llm_infos = "", {}
         # se a resposta for muito longa, tenta gerar uma resposta mais curta
        if llm_infos.get('n_chars', 0) > 500:
            old_llm_response, old_llm_infos = llm_response, llm_infos.copy()
            llm_response, llm_infos = llm.get_response(
                user_response + "(Responda de forma mais breve e resumida)"
                )
            retry_audio = True

        voice = determine_voice(id_empresa=id_empresa)
        logger.info(f" [*] [messages_received_worker] Determined voice: {voice}")
        message_data, tts_seconds = convert_to_audio(llm_response, voice = voice) # audio em binário
        message_type = "audio"
    else:
        retry_audio = False
        old_llm_response, old_llm_infos = "", {}
        tts_seconds = 0

    logger.info(f"[RECEIVED] origin {origin}")

    if is_retry:
        if not os.getenv('RETRY_MESSAGE', 'false') == 'true':
            logger.info(" [*] [messages_received_worker] Retry message is disabled")
            return log_response({
                "data": {
                    "status": "error",
                    "phone": telefone_envio,
                    "response": (
                        "INFORMAÇÃO DO SISTEMA: O usuário não respondeu à sua mensagem, "
                        "por favor, fale educadamente com ele novamente."
                    ),
                    "id_empresa": id_empresa,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            })


    before_send_message = datetime.now()

    sent_message_id = messager.send_message(
        message_data=message_data,
        phone=telefone_envio,
        flow=Config.FLOW_NAME,
        id_empresa=id_empresa,
        message_type=message_type,
        message_id=message_id,
        is_group=is_group,
        id_session=id_session,
        connected_phone=parse_phone(data.get('connectedPhone'), origin=origin),
        imagem = llm_infos.get('imagem', None)
    )

    task["tempo_processamento_meta"]["tempo_enviar_mensagem"] = (datetime.now() - before_send_message).total_seconds()

    if not is_retry:
        momento_envio = datetime.now().timestamp()
        try:
            momento_recebimento = task.pop("momento_recebimento")
            tempo_resposta = momento_envio - momento_recebimento
            meta = task.pop("tempo_processamento_meta")
            meta["provedor_mensagem"] = messager.provider
            register_indicator(
                "tempo_resposta",
                id_empresa,
                telefone=telefone,
                indicador=str(tempo_resposta),
                nome=user_context.get("aluno", {}).get("pessoa", {}).get("nome", ""),
                meta=meta
            )
        except KeyError:
            logger.warning("Indicador 'tempo_resposta' não será registrado!")

    if info_pending_forwarding := llm.get_info_pending_forwarding():
        _, telefone_responsavel = bq_.get_responsible_data()
        if isinstance(telefone_responsavel, str):
            for info in info_pending_forwarding:
                logger.info(f"\n\n{info}\n\n")
                if not isinstance(info, str):
                    continue
                telefone_responsavel = parse_phone(telefone_responsavel, "gym_bot")
                if messager.send_message(
                    message_data=info,
                    phone=telefone_responsavel,
                    id_empresa=id_empresa,
                    message_type="text",
                    message_id=None,
                    is_group=False,
                    connected_phone=parse_phone(
                        data.get('connectedPhone'), origin=origin
                    ),
                ):
                    logger.info(
                        f"[BOOK_CLASS_ERROR] Mensagem enviada para o responsável {telefone_responsavel}!!!"
                    )
                else:
                    logger.warning(
                        f"[BOOK_CLASS_ERROR] Mensagem não enviada para o responsável {telefone_responsavel}!!!"
                    )
        else:
            logger.warning(
                "[BOOK_CLASS_ERROR] Atenção, sem telefone configurado para avisar que deu ruim na IA!!!"
            )

    save_and_sync_messages(
        id_empresa=id_empresa, user_response=user_response, telefone=telefone,
        user_msg_type=user_msg_type, user_info_msg=user_info_msg, message_id=message_id,
        messager_provider=messager.provider, id_contexto=id_contexto, situacao=situacao,
        departamento=departamento, colaborador=colaborador, send_audio=send_audio,
        llm_infos=llm_infos, old_llm_infos=old_llm_infos, llm_response=llm_response,
        old_llm_response=old_llm_response, retry_audio=retry_audio, sent_message_id=sent_message_id,
        tts_seconds=tts_seconds, user_context=user_context, current_time_user=current_time_user
    )

    bq_.save_connected_phone(parse_phone(data.get('connectedPhone')))

    unset_pendency_verification(telefone, id_empresa)
    if not data.get('pendency_processed', False):
        set_pendency_verification(telefone, id_empresa, task)

    return log_response({
        "data": {
            "status": "success",
            "phone": telefone_envio,
            "response": llm_response,
            "id_empresa": id_empresa,
            "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
        },
        "table" : "mensagens_enviadas"
    })

@WorkersTracer(
    span_name_prefix=f"{__name__}.handle_received_message_rede",
    span_description="Processando a mensagem recebida",
    span_attributes={
        "task": "task",
        "id_empresa": "id_empresa",
        "telefone": "telefone",
        "origin": "origin",
        "situacao": "situacao",
        "canAnswer": "canAnswer"
    }
)
def handle_received_message_rede(task):
    if not task.get("tempo_processamento_meta", None):
        task['tempo_processamento_meta'] = {}
    # Obter a data e hora atual no formato ISO 8601
    before_setup = datetime.now()
    current_time_user = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')

    redis_client = connections.redis_client

    model_source = task['model_source']
    canAnswer = task['canAnswer']
    data = task['data']
    data = json.loads(json.dumps(data))
    situacao = (data.get("aluno", {}).get("situacao", {}).get("codigo") or
    data.get("aluno", {}).get("pessoa", {}).get("situacao") or
    data.get("aluno", {}).get("situacao") or
    None)
    origin = task.get('origin', 'z_api')
    departamento = task['data']['departamento']
    colaborador = task['data']['colaborador']

    testing = False
    if data.get("sendToWebhook", None):
        testing = True

    testing = False
    if data.get("sendToWebhook", None):
        testing = True

    if data.get('chave_empresa', None) is None:
        instance_id = data.get('instanceId')
        connected_phone = parse_phone(data.get('connectedPhone'))
        id_matriz = task['id_matriz']

        if testing:
            id_matriz = data.get('testCompanyId', None)
            if not id_matriz:
                logger.error(" [*] [messages_received_worker] Test company ID not found")
                return
            origin = "auto_tests"

        logger.info(f'ID_MATRIZ: {id_matriz}')
        logger.info(f'INSTANCE_ID: {instance_id}')
        logger.info(f'MENSAGEM RECEBIDA:\n{json.dumps(data, indent=4)}')

        if id_matriz is None:
            logger.info(f" [*] [messages_received_worker] Empresa não encontrada")
            return log_response({
                "data":{
                    "status": "error",
                    "phone": data.get('phone', data.get('telefone', None)),
                    "response": "Empresa não encontrada",
                    "id_empresa": None,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            })
        is_group = bool(data.get('isGroup', False))
        user_info_msg, content = dp.process_message(data)
        logger.info(f" [*] [messages_received_worker] Processou mensagem!")
        user_msg_type = user_info_msg.get('type', None)
        if user_info_msg is None:
            return log_response({
                "data":{
                    "status": "error",
                    "phone": data.get('phone', data.get('telefone', None)),
                    "response": "Não há dados na mensagem",
                    "id_empresa": id_matriz,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            })
        message_id = data.get('messageId', None)
        id_session = data.get('sessionId', None)

        if user_msg_type == "audio":
            user_response = str(content)
        elif user_msg_type == "text":
            logger.info(f"[DEBUG] Text message received")
            phone = parse_phone(data.get('phone', None))
            user_response = redis_client.get(f"recent_messages:{id_matriz}:{phone}")
            if user_response:
                user_response = user_response.decode('utf-8')
            else:
                logger.warning(" [*] [messages_received_worker] No recent messages found for %s, empresa %s", phone, id_matriz)
                user_response = content.get("message")
            redis_client.delete(f"recent_messages:{id_matriz}:{phone}")
            redis_client.delete(f"messages_tasks:{id_matriz}:{phone}:task_id")
        elif user_msg_type == "notification":
            return log_response({"success": "No response needed"})
        elif user_msg_type is None:
            user_response = "O usuário te mandou um tipo de mídia não suportado"
        else:
            user_response = f"O usuário te mandou uma mensagem do tipo {user_msg_type}, esse é o conteúdo: {json.dumps(content)}"

    else:
        id_matriz = data.get('chave_empresa', None)
        telefone = data.get('telefone', None)
        user_response = data.get('mensagem', None)
        is_group = False
        message_id = None
        connected_phone = None
        id_session = None

    if is_group:
        telefone = parse_phone(data.get('participantPhone', None))
        telefone_envio = data.get('phone', None)

    elif not is_group:
        telefone = parse_phone(data.get('phone', data.get('telefone', None)))
        telefone_envio = telefone
    
    else:
        logger.info(f" [*] [messages_received_worker] Erro ao identificar o telefone")
        return log_response({
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Erro ao identificar o telefone",
                "id_empresa": id_matriz,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })
    task["tempo_processamento_meta"]["tempo_validacao_dos_dados"] = (datetime.now() - before_setup).total_seconds()

    before_context = datetime.now()
    logger.info(f" [*] [messages_received_worker] Criando o BQ...")
    bq_ = bq(id_empresa=None, id_matriz=id_matriz)
    id_empresa = bq_.get_id_empresa_from_matriz(telefone)
    id_empresa = id_empresa if id_empresa else id_matriz
    bq_.set_id_empresa(id_empresa)

    user_context, fase_atual = bq_.get_user_context(telefone, use_id_matriz=True, use_id_empresa=bool(id_empresa))

    if not user_context:
        nome = data.get('senderName', 'DESCONHECIDO')
        z_api_first_message(
            telefone=telefone,
            id_empresa=id_empresa,
            id_matriz=id_matriz,
            is_rede=True,
            fase_crm="LEADS_HOJE",
            redis_client=redis_client,
            nome=nome
        )

        id_empresa = bq_.get_id_empresa_from_matriz(telefone) or id_empresa
        bq_.set_id_empresa(id_empresa)

        user_context, fase_atual = bq_.get_user_context(telefone=telefone, use_id_matriz=True, use_id_empresa=bool(id_empresa))

    task["tempo_processamento_meta"]["tempo_busca_dados_usuario"] = (datetime.now() - before_context).total_seconds()

    before_validation = datetime.now()
    if origin == 'gym_bot' and canAnswer:
        # isso é necesssário devido as mensagens do retry_contact que não passam pelo task_adapter
        # Por isso, pode ser que quando retry entrou na fila o usuário estava no departamento da IA e agora já está em outro.
        canAnswer = verify_can_answer(id_session, id_matriz)

    if not canAnswer:
        unset_retry_contact(telefone_envio, id_empresa)
        logger.info(f"LLM não pode responder!")
        user_context, id_contexto = bq_.get_user_context(telefone, use_id_matriz=True, use_id_empresa=bool(id_empresa))
        bq_.save_message(
            "user",
            user_response,
            telefone,
            message_type=user_msg_type,
            n_chars=len(user_response),
            n_seconds=user_info_msg.get('n_seconds', 0),
            message_id=message_id,
            provider=origin,
            id_contexto=id_contexto,
            situacao=situacao
        )
        if user_context and 'aluno' in user_context and 'codigo' in user_context['aluno']:
            if user_context['aluno']['codigo']:
                user_task = {"id_empresa":id_empresa,
                            "message":f"USER: {user_response}",
                            "cliente": user_context['aluno']['codigo'],
                            "tipoContato": "WA",
                            "fase": id_contexto,
                            "data": current_time_user}
                redis_client.lpush('task_queue_crm_msg_history', json.dumps(user_task))

        return log_response({
            "data": {
                "status": "success",
                "phone": telefone_envio,
                "response": "IA não pode responder, pois o usuário está em outro departamento",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })

    if not validate_notification(
        {**data, **user_context}
    ):
        logger.info(" [*] [messages_received_worker] Notificação inválida, ignorando")
        return log_response({
            "data": {
                "status": "error",
                "phone": telefone_envio,
                "response": "Usuário não está em uma situação que condiz com a notificação",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table": "mensagens_enviadas"
        })

    logger.info(f" [*] [messages_received_worker] user_context: {json.dumps(user_context, indent=2, ensure_ascii=False)}")
    logger.info(f" [*] [messages_received_worker] id_empresa: {id_empresa}")

    if check_strikes(telefone, id_empresa, redis_client, bq_):
        return log_response({
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Usuário bloqueado por comportamento malicioso.",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })

    if ignore_message(data.get('momment')):
        return log_response({
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Mensagem muito antiga.",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })

    task["tempo_processamento_meta"]["tempo_validacao_usuario"] = (datetime.now() - before_validation).total_seconds()
    
    test_args = {
        "webhook_url": data.get('webhookUrl', None),
        "test_name": data.get('testName', None)
    }

    is_new_conversation = False
    try:
        new_conversation = task.get("new_conversation")
        logger.info("[IS_NEW_CONVERSATION] %s", new_conversation)
        task["new_conversation"] = False
        is_new_conversation = new_conversation
    except KeyError:
        pass

    before_context_update = datetime.now()
    if is_new_conversation:
        register_indicator(
            "conversa_iniciada",
            id_empresa,
            telefone=telefone,
            indicador="passivo",
            nome=user_context.get("aluno", {}).get("pessoa", {}).get("nome", "")
        )
        pacto_tools = PactoIntegrationTools(id_empresa)
        id_usuario = user_context.get("aluno", {}).get("codigo")
        if id_usuario:
            user_data = pacto_tools.get_user_by_id(id_usuario)
            if user_data:
                data = {
                    "type": "user",
                    "id_empresa": id_empresa,
                    "id_matriz": id_matriz,
                    "data":
                        {
                            "telefone": telefone,
                            "contexto": user_data,
                            "fase": fase_atual,
                            "origin_last_update": "new_conversation"
                        }
                }
                redis_client.lpush('task_queue_bd_contexts', json.dumps(data))
                redis_client.set(f"{telefone}-{id_empresa}", json.dumps(user_data))
                user_context = user_data

    task["tempo_processamento_meta"]["tempo_atualizacao_contexto_usuario"] = (datetime.now() - before_context_update).total_seconds()

    before_llm = datetime.now()

    llm = LLMSelector(
        user_context=user_context,
        id_empresa=id_empresa,
        phase=fase_atual, 
        telefone=telefone,
        is_group=is_group,
        id_matriz=id_matriz,
        models_source=model_source,
        is_rede=True,
        origin=origin,
        id_session=id_session,
        send_logs_to=test_args
    )
    task["tempo_processamento_meta"]["tempo_construcao_prompt"] = (datetime.now() - before_llm).total_seconds()
    
    messager = WhatsappMessager(
        origin, 
        id_empresa=id_matriz,
        test_args=test_args,
    )

    is_retry = task.get('retries', 0) > 0
    if (retry_response := handle_retry_message(
            task,
            telefone_envio,
            id_empresa,
            bq_,
            llm,
            messager
            )) is not None:
        return retry_response

    if (command := parse_command(user_response, telefone_envio, id_empresa)) != Command.NOOP:
        parse_command(user_response, telefone_envio, id_matriz, for_matriz=True)

        unset_retry_contact(telefone_envio, id_empresa)
        unset_pendency_verification(telefone, id_empresa)
        message = command.value
        return_value = {}
        match command:
            case Command.DONE:
                return_value = {
                    "data": {
                        "status": "success",
                        "phone": telefone_envio,
                        "response": "Operação realizada!",
                        "id_empresa": id_empresa,
                        "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                    },
                }
            case Command.FINISH:
                llm.end_conversation()
                return_value = {
                    "data": {
                        "status": "success",
                        "phone": telefone_envio,
                        "response": "Conversa finalizada",
                        "id_empresa": id_empresa,
                        "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                    },
                }
        messager.send_message(
            message_data=message,
            phone=telefone_envio,
            flow=Config.FLOW_NAME,
            id_empresa=id_empresa,
            message_type="text",
            message_id=message_id,
            is_group=is_group,
            id_session=id_session,
            connected_phone=parse_phone(data.get('connectedPhone'), origin=origin),
        )
        return log_response(return_value)

    before_llm_message = datetime.now()

    llm_response, llm_infos = llm.get_response(user_response)

    task["tempo_processamento_meta"]["tempo_gerar_resposta_llm"] = (datetime.now() - before_llm_message).total_seconds()

    # Essa linha a baixo é necessária porque um aluno LEAD pode ter escolhido
    # uma academia da rede nessa exata interação com a IA. Nessa situação, a partir daqui
    # o bq já vai salvar as mensagens com a empresa escolhida pelo aluno.
    reset_conversation_to_force_chain_messages_reload = False
    if llm.get_id_empresa() != bq_.id_empresa:
        logger.info(f" [*] [messages_received_worker] Mudando id_empresa para {llm.get_id_empresa()}")
        bq_.set_id_empresa(llm.get_id_empresa())
        reset_conversation_to_force_chain_messages_reload = True

    logger.info(f"Resposta LLM:\n{llm_response}")
    if llm_response == "FINISH":
        messager.end_conversation(
            phone=telefone_envio,
            id_empresa=id_empresa,
            sticker=bq_.get_sticker()
            )
        unset_retry_contact(telefone_envio, id_empresa)
        unset_pendency_verification(telefone, id_empresa)
        return log_response({
            "data": {
                "status": "success",
                "phone": telefone_envio,
                "response": "Conversa finalizada",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
        })

    send_audio = can_send_audio(text=llm_response, last_message_type=user_msg_type, info_llm=llm_infos)
    message_type = "image" if llm_infos.get('is_campanha', False) else "text"
    message_data = llm_response

    # TODO: Verificar se pode ter conflito o audio com recurso de campanha
    if send_audio:
        retry_audio = False
        old_llm_response, old_llm_infos = "", {}
         # se a resposta for muito longa, tenta gerar uma resposta mais curta
        if llm_infos.get('n_chars', 0) > 500:
            old_llm_response, old_llm_infos = llm_response, llm_infos.copy()
            llm_response, llm_infos = llm.get_response(
                user_response + "(Responda de forma mais breve e resumida)"
                )
            retry_audio = True

        voice = determine_voice(id_empresa=id_empresa)
        logger.info(f" [*] [messages_received_worker] Determined voice: {voice}")
        message_data, tts_seconds = convert_to_audio(llm_response, voice = voice) # audio em binário
        message_type = "audio"
    else:
        retry_audio = False
        old_llm_response, old_llm_infos = "", {}
        tts_seconds = 0
    logger.info(f" [*] [messages_received_worker] message_data: {message_data}")

    if is_retry:
        if not os.getenv('RETRY_MESSAGE', 'false') == 'true':
            logger.info(" [*] [messages_received_worker] Retry message is disabled")
            return log_response({
                "data": {
                    "status": "error",
                    "phone": telefone_envio,
                    "response": (
                        "INFORMAÇÃO DO SISTEMA: O usuário não respondeu à sua mensagem, "
                        "por favor, fale educadamente com ele novamente."
                    ),
                    "id_empresa": id_empresa,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            })

    before_send_message = datetime.now()

    sent_message_id = messager.send_message(
        message_data=message_data,
        phone=telefone_envio,
        flow=Config.FLOW_NAME,
        #id_empresa=id_empresa,
        id_empresa=id_matriz,
        message_type=message_type,
        message_id=message_id,
        id_session=id_session,
        connected_phone=parse_phone(data.get('connectedPhone'), origin=origin),
        imagem = llm_infos.get('imagem', None),
    )

    task["tempo_processamento_meta"]["tempo_enviar_mensagem"] = (datetime.now() - before_send_message).total_seconds()

    if not is_retry:
        momento_envio = datetime.now().timestamp()
        try:
            momento_recebimento = task.pop("momento_recebimento")
            tempo_resposta = momento_envio - momento_recebimento
            meta = task.pop("tempo_processamento_meta")
            meta["provedor_mensagem"] = messager.provider
            register_indicator(
                "tempo_resposta",
                id_empresa,
                telefone=telefone,
                indicador=str(tempo_resposta),
                nome=user_context.get("aluno", {}).get("pessoa", {}).get("nome", "")
            )
        except KeyError:
            logger.warning("Indicador 'tempo_resposta' não será registrado!")

    if info_pending_forwarding := llm.get_info_pending_forwarding():
        _, telefone_responsavel = bq_.get_responsible_data()
        if isinstance(telefone_responsavel, str):
            for info in info_pending_forwarding:
                logger.info(f"\n\n{info}\n\n")
                if not isinstance(info, str):
                    continue
                telefone_responsavel = parse_phone(telefone_responsavel, "gym_bot")
                if messager.send_message(
                    message_data=info,
                    phone=telefone_responsavel,
                    id_empresa=id_matriz,
                    message_type="text",
                    message_id=None,
                    is_group=False,
                    connected_phone=parse_phone(
                        data.get('connectedPhone'), origin=origin
                    ),
                ):
                    logger.info(
                        f"[BOOK_CLASS_ERROR] Mensagem enviada para o responsável {telefone_responsavel}!!!"
                    )
                else:
                    logger.warning(
                        f"[BOOK_CLASS_ERROR] Mensagem não enviada para o responsável {telefone_responsavel}!!!"
                    )
        else:
            logger.warning(
                "[BOOK_CLASS_ERROR] Atenção, sem telefone configurado para avisar que deu ruim na IA!!!"
            )

    save_and_sync_messages(
        id_empresa=llm.get_id_empresa(), user_response=user_response, telefone=telefone,
        user_msg_type=user_msg_type, user_info_msg=user_info_msg, message_id=message_id,
        messager_provider=messager.provider, id_contexto=fase_atual, situacao=situacao,
        departamento=departamento, colaborador=colaborador, send_audio=send_audio,
        llm_infos=llm_infos, old_llm_infos=old_llm_infos, llm_response=llm_response,
        old_llm_response=old_llm_response, retry_audio=retry_audio, sent_message_id=sent_message_id,
        tts_seconds=tts_seconds, user_context=user_context, current_time_user=current_time_user,
        reset_conversation=reset_conversation_to_force_chain_messages_reload,
    )

    bq_.save_connected_phone(connected_phone)

    unset_pendency_verification(telefone, id_empresa)
    if (
        not data.get('pendency_processed', False)
        ) and (
        llm_response is not None
        ) and (
        not is_retry
    ):
        set_pendency_verification(telefone, id_empresa, task)

    return log_response({
        "data": {
            "status": "success",
            "phone": telefone_envio,
            "response": llm_response,
            "id_empresa": id_empresa,
            "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
        },
        "table" : "mensagens_enviadas"
        })

@WorkersTracer(
    span_name_prefix=f"{__name__}.router_received_message",
    span_description="Decidindo o handler",
    span_attributes={
        "task": "task",
        "id_empresa": "id_empresa",
        "telefone": "telefone",
        "origin": "origin",
        "situacao": "situacao",
        "canAnswer": "canAnswer"
    }
)
def router_received_message(task):
    """Decide qual handler usar"""
    # Obter a data e hora atual no formato ISO 8601
    if task.get("momento_enfileiramento", None):
        momento_enfileiramento = task.pop("momento_enfileiramento")
        if not task.get("tempo_processamento_meta", None):
            task["tempo_processamento_meta"] = {}
        momento_enfileiramento_datetime = datetime.fromtimestamp(momento_enfileiramento)
        task["tempo_processamento_meta"]["tempo_espera_mensagem"] = (datetime.now() - momento_enfileiramento_datetime).total_seconds()
    else:
        task["tempo_processamento_meta"] = {}
        task["tempo_processamento_meta"]["tempo_espera_mensagem"] = 0

    before_route = datetime.now()

    logger.info(f" [*] [router_received_message] Decidindo o handler")
    id_empresa, _ = get_from_instance(task.get("data", {}).get("instanceId", None))
    is_rede = _is_rede(task.get("data", {}).get("instanceId", None))

    if task.get("data", {}).get("sendToWebhook", None):
        id_empresa = task.get("data", {}).get('testCompanyId', None)
        if not id_empresa:
            logger.error(" [*] [messages_received_worker] Test company ID not found")
            return
        is_rede = task.get("data", {}).get("isRede", False)

    bq_ = bq(id_empresa=id_empresa)
    gym_data = bq_.get_gym_context()
    is_rede = gym_data.get("is_rede", is_rede)
    model_source = bq_.get_model_source()
    task["model_source"] = model_source
    task["tempo_processamento_meta"]["tempo_identificar_modo_empresa"] = (datetime.now() - before_route).total_seconds()
    if is_rede:
        logger.info(f" [*] [router_received_message] encaminhado para o handle rede")
        task["id_matriz"] = id_empresa
        handle_received_message_rede(task)

    else:
        logger.info(f" [*] [router_received_message] encaminhado para o handle normal")
        task["id_empresa"] = task["id_empresa"] if task["id_empresa"] else id_empresa
        handle_received_message(task)

@WorkersTracer(
    span_name_prefix=f"{__name__}.run",
    span_description="Rodando o worker",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter"
    }
)
def run(redis_client, max_iter=None):
    """Worker para mensagens recebidas"""
    logger.info(" [*] [messages_received_worker] Waiting for tasks")
    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        queue_size = redis_client.llen('messages_received')
        logger.info(f"[x] [messages_received_worker] Task queue size: {queue_size}")
        task = redis_client.brpop('messages_received')
        if task:
            logger.info(f"\n\n\n{'Nova Task':-^100}\n\n\n")
            task = json.loads(task[1].decode('utf-8'))
            task["momento_enfileiramento"] = datetime.now().timestamp()

            try:
                logger.info(" TENTANDO PROCESSAR A TASK ")
                logger.info(task)
                # add response to the sent messages list
                # Pega o texto, telefone e id_empresa da task
                text = task.get('data', {}).get('text', {}).get('message', None)
                phone = parse_phone(task.get('data', {}).get('phone', None))

                task_id = task.get('task_uid')

                logger.info(f" [*] [messages_received_worker] phone: {phone}")

                add_custom_handler(logger, phone, task_id)

                instance_id = task.get('data', {}).get('instanceId')
                id_empresa = task.get('id_empresa', None)
                if id_empresa is None:
                    id_empresa, _ = get_from_instance(instance_id)

                if text is None or phone is None or id_empresa is None:
                    logger.info("Retrocompatibilidade")
                    # Para manter retrocompatibilidade, mas não é pra cair aqui
                    # logger.error(" [*] [messages_received_worker] Error: text, phone or id_empresa is None")
                    router_received_message(task)
                    iter_count += 1
                    continue

                # Adiciona a mensagem à lista de mensagens recentes
                connections.redis_client.append(
                    f"recent_messages:{id_empresa}:{phone}",
                    f"{text}\n"
                )
                # Verifica se já existe um task para esse telefone
                if not connections.redis_client.exists(
                    f"messages_tasks:{id_empresa}:{phone}:task_id"
                    ):
                    # Cria um task para lidar com a mensagem depois de X segundos
                    context = task.get('context', {})
                    task_id = delayed_queue.delay_call(
                        func=router_received_message,
                        args={"task": task},
                        seconds=SECONDS_TO_WAIT_TILL_RESPONSE,
                        context=context
                    )
                    connections.redis_client.set(
                        f"messages_tasks:{id_empresa}:{phone}:task_id",
                        task_id,
                        ex=5*60
                    )

            except Exception as e:
                import traceback
                #logger.error(f"Error: {e}")
                traceback_ = traceback.format_exc()
                #logger.error(traceback_)
                data = task.get('data', {})
                response = {
                    "data": {
                        "status": "error",
                        "phone": data.get('phone', data.get('telefone', None)),
                        "response": str(e),
                        "id_empresa": task.get('id_empresa', None),
                        "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                    },
                    "table" : "mensagens_enviadas"
                }
                redis_client.lpush('logs', json.dumps(response))
                traceback.print_exc()
                logger.error(f" [x] [messages_received_worker] Error: {e}")

        iter_count += 1
