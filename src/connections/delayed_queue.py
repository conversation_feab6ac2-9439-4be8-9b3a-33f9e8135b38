"""
<PERSON><PERSON> módulo define a classe DelayedQueue,
que é uma fila de mensagens que podem ser enviadas em um tempo futuro.
"""

import concurrent.futures
import json
import logging
import os
import time
import traceback
from concurrent.futures import TimeoutError
from datetime import datetime
from typing import Callable
from uuid import uuid4

import redis

from src.worker.tracing import tracer, inject, extract
from src.extras.util import add_custom_handler

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
TASK_TIMEOUT = int(os.getenv("TASK_TIMEOUT", 360))
DELAYED_QUEUE_MAX_WORKERS = int(os.getenv("DELAYED_QUEUE_MAX_WORKERS", "1"))
DELAYED_QUEUE = "delayed_messages"
QUEUE_DATA = "queue_data"

logger = logging.getLogger("conversas_logger")


class DelayedQueue:
    """
    A classe DelayedQueue é uma fila de mensagens que podem ser enviadas em um tempo futuro.

    Podem ser passadas tanto funções para serem chamadas quanto mensagens para serem enviadas
    para uma fila específica.

    **É importante que a função a ser chamada esteja disponível no escopo global.**
    """

    instance: "DelayedQueue" = None

    def __init__(self):
        """
        Inicializa a fila de mensagens.
        """
        self.redis_client = redis.from_url(REDIS_URL)

    def __new__(cls):
        if not cls.instance:
            cls.instance = super().__new__(cls)
        return cls.instance

    def time_to_seconds(
        self, hours: int = 0, minutes: int = 0, seconds: int = 0
    ) -> int:
        """
        Converte horas, minutos e segundos em segundos.

        Params:
            hours (int): O número de horas.
            minutes (int): O número de minutos.
            seconds (int): O número de segundos.

        Returns:
            int: O total de segundos.
        """
        return hours * 3600 + minutes * 60 + seconds

    def schedule_message(
        self, message: dict, delay_seconds: int, task_id: str, task_type: str
    ):
        # Garante unicidade com base no user_id e timestamp
        message_data = json.dumps(
            {
                "id": task_id,
                "type": task_type,
                "content": message,
                "processed": False,  # Flag para evitar reprocessamento
            }
        )
        # Timestamp de entrega (atual + atraso)
        delivery_time = int(time.time()) + delay_seconds
        # Adiciona ao Sorted Set (score = delivery_time)
        if not self.redis_client.hexists(QUEUE_DATA, task_id):
            # Armazena os dados completos da mensagem no hash, usando task_id como chave
            self.redis_client.hset(QUEUE_DATA, task_id, message_data)
    
            self.redis_client.zadd(DELAYED_QUEUE, {message_data: delivery_time})
            logger.info(
                f"Mensagem agendada para {datetime.fromtimestamp(delivery_time)} (ID: {task_id})"
            )
        else:
            logger.warning(
                f"Mensagem com ID {task_id} já agendada, ignorando duplicação."
            )

    def delay_enqueue(
        self,
        queue: str = "default",
        task: dict = {},
        hours: int = 0,
        minutes: int = 0,
        seconds: int = 0,
    ):
        """
        Adiciona uma mensagem à fila com um atraso de delay segundos.

        Params:
            queue (str): O nome da fila para enviar.
            task (dict): A mensagem a ser enviada.
            hours (int): O número de horas para atrasar o envio.
            minutes (int): O número de minutos para atrasar o envio.
            seconds (int): O número de segundos para atrasar o envio.
        """
        task_id = str(uuid4())
        seconds = self.time_to_seconds(hours, minutes, seconds)
        task = {"queue": queue, "task": task, "delay": seconds}
        self.schedule_message(task, seconds, task_id, "queue")

        return task_id

    def delay_call(
        self,
        func: Callable,
        args: dict = {},
        hours: int = 0,
        minutes: int = 0,
        seconds: int = 0,
        context: dict = {},
    ) -> str:
        """
        Programa uma função para ser chamada em um tempo futuro.

        Params:
            func (Callable): O caminho completo da função a ser chamada.
            args (dict): Os argumentos da função.
            hours (int): O número de horas para atrasar a chamada.
            minutes (int): O número de minutos para atrasar a chamada.
            seconds (int): O número de segundos para atrasar a chamada.

        Returns:
            str: O ID da tarefa agendada.
        """
        func_path = func.__module__ + "." + func.__name__
        logger.info(func_path)
        task_id = str(uuid4())
        seconds = self.time_to_seconds(hours, minutes, seconds)
        with tracer.start_as_current_span("delay_call", context=extract(context)) as span:
            task = {"func": func_path, "args": args, "delay": seconds}
            span.set_attribute("func", func_path)
            span.set_attribute("delay", seconds)
            span.set_attribute("task_id", task_id)
            span.set_attribute("args", json.dumps(args))
            carrier = {}
            inject(carrier)
            task["context"] = carrier
            self.schedule_message(task, seconds, task_id, "call")

        return task_id

    def cancel_task(self, task_id: str):
        """
        Deleta uma tarefa agendada.

        Params:
            task_id (str): O ID da tarefa a ser deletada.
        """
        if self.redis_client.hexists(QUEUE_DATA, task_id):
            message_data = self.redis_client.hget(QUEUE_DATA, task_id)
            if message_data:
                json_data = json.loads(message_data)
                if json_data["type"] == "call":
                    func = json_data["content"]["func"]
                    args = json_data["content"]["args"]
                    context = json_data["content"]["context"]
                    with tracer.start_as_current_span("cancel_task", context=extract(context)) as span:
                        span.set_attribute("func", func)
                        span.set_attribute("task_id", task_id)
                        span.set_attribute("args", json.dumps(args))

                self.redis_client.zrem(DELAYED_QUEUE, message_data)
                self.redis_client.hdel(QUEUE_DATA, task_id)
                logger.info(f"Tarefa cancelada com sucesso (ID: {task_id})")
            else:
                logger.warning(f"Tarefa não encontrada (ID: {task_id})")

    def find_func(self, func: str) -> Callable:
        """
        Encontra a função a ser chamada.

        Params:
            func (str): O caminho completo da função a ser chamada.

        Returns:
            Callable: A função a ser chamada.
        """
        if "." in func:
            module_name, func_name = func.rsplit(".", 1)
            module = __import__(module_name, fromlist=[func_name])
            logger.info(f"Importando módulo: {module_name}")
            return getattr(module, func_name)
        elif func in globals():
            return globals()[func]
        else:
            raise ValueError(f"Function {func} not found")

    def enqueue(self, queue: str, task: dict):
        """
        Adiciona uma mensagem à fila.

        Params:
            queue (str): O nome da fila para enviar.
            task (dict): A mensagem a ser enviada.
        """
        self.redis_client.rpush(queue, json.dumps(task))

    def make_call(self, func: str, args: dict, task_id: str, context: dict):
        """
        Chama uma função.

        Params:
            func (str): O caminho completo da função a ser chamada.
            args (dict): Os argumentos da função.
        """
        callable_func = self.find_func(func)
        with tracer.start_as_current_span("make_call", context=extract(context)) as span:
            span.set_attribute("func", func)
            span.set_attribute("task_id", task_id)
            span.set_attribute("args", json.dumps(args))
            task = args.get("task") or args.get("original_task")
            if isinstance(task, dict):
                phone_number = task.get("data", {}).get("phone", '')
                task_uid = task.get("task_uid")
                add_custom_handler(logger, phone_number, task_uid)
            callable_func(**args)

class DelayedQueueWorker:
    """
    Worker para processar mensagens da fila atrasada.
    """

    instance: "DelayedQueueWorker" = None

    def __init__(self):
        self.redis_client = redis.from_url(REDIS_URL)
        self.delayed_queue = DelayedQueue()

    def __new__(cls):
        if not cls.instance:
            cls.instance = super().__new__(cls)
        return cls.instance

    def process_message(self, msg_data: dict):
        """
        Processa a mensagem conforme o tipo da tarefa.
        """
        task_type = msg_data["type"]
        task_id = msg_data["id"]
        content = msg_data["content"]
        context = content.get("context", {})

        logger.info(f"[PROCESS_MESSAGE] Processando mensagem: {msg_data})")

        if task_type == "queue":
            self.delayed_queue.enqueue(content["queue"], content["task"])
        elif task_type == "call":
            self.delayed_queue.make_call(content["func"], content["args"], task_id, context)

    def run(self, *args, **kwargs):  # noqa
        """
        Inicia o worker da fila atrasada.
        """
        logger.info("[DelayedQueue] Iniciando worker...")
        while True:
            try:
                current_time = int(time.time())
                # Busca mensagens cujo score <= tempo atual (com limite para evitar sobrecarga)
                messages = self.redis_client.zrangebyscore(
                    DELAYED_QUEUE, 0, current_time, start=0, num=100
                )
                # Remove mensagens para não processar novamente
                if messages:
                    logger.info(
                        f"[DelayedQueue] Processando {len(messages)} mensagens da fila atrasada..."
                    )
                    self.redis_client.zrem(DELAYED_QUEUE, *messages)
                else:
                    continue

                for msg in messages:
                    msg_data = json.loads(msg)
                    if msg_data["processed"]:  # Verifica se já foi processada
                        # Remove a mensagem já processada
                        self.redis_client.zrem(DELAYED_QUEUE, msg)
                        self.redis_client.hdel(QUEUE_DATA, msg_data["id"])
                        continue

                    logger.info(
                        f"Worker processando mensagem de fila atrasada: {msg_data['content']} (ID: {msg_data['id']})"
                    )
                    # Marca a mensagem como processada e agenda remoção/manutenção no Sorted Set
                    updated_data = json.dumps({**msg_data, "processed": True})
                    self.redis_client.zrem(DELAYED_QUEUE, msg)
                    self.redis_client.hset(QUEUE_DATA, msg_data["id"], updated_data)

                    # Processa a tarefa com controle de timeout usando ThreadPoolExecutor
                    with concurrent.futures.ThreadPoolExecutor(max_workers=DELAYED_QUEUE_MAX_WORKERS) as executor:
                        future = executor.submit(self.process_message, (msg_data))
                        try:
                            future.result(timeout=TASK_TIMEOUT)
                        except TimeoutError as e:
                            logger.error(
                                f"Tarefa {msg_data['id']} excedeu o tempo limite de {TASK_TIMEOUT} segundos."
                            )

                time.sleep(1)  # Reduz a frequência para evitar sobrecarga
            except Exception as e:
                logger.error(f"Erro no worker: {e}")
                traceback.print_exc()
                time.sleep(1)  # Pausa em caso de erro
                raise e
