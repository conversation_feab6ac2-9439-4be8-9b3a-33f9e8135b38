#!/bin/bash

# Script simples para rodar migrate-docker-compose.yml e abrir no browser
set -Eeuo pipefail

COMPOSE_FILE="migrate-docker-compose.yml"

echo "🚀 Iniciando migrate-docker-compose.yml..."

# Para containers existentes
docker-compose -f "$COMPOSE_FILE" down

# Inicia os serviços
docker-compose -f "$COMPOSE_FILE" up --build -d
 
echo "✅ Migrate big query para postgres iniciado!"
docker-compose -f "$COMPOSE_FILE" logs -f
